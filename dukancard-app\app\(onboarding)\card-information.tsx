import CategoryBottomSheetPicker, {
  CategoryBottomSheetPickerRef,
} from "@/src/components/pickers/CategoryBottomSheetPicker";
import { BUSINESS_CATEGORIES } from "@/lib/config/categories";
import { OnboardingContainer } from "@/src/components/layout/OnboardingContainer";

import { useSlugValidation } from "@/src/hooks/useSlugValidation";
import { useTheme } from "@/src/hooks/useTheme";
import { useAuthErrorHandler } from "@/src/hooks/useAuthErrorHandler";

import { InlineErrorHandler } from "@/src/components/ui/InlineErrorHandler";
import { ErrorRecovery } from "@/src/components/ui/ErrorRecovery";
import {
  getOnboardingData,
  saveOnboardingData,
} from "@/backend/supabase/services/common/onboardingService";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import { Briefcase, Link, Loader2, Phone, User } from "lucide-react-native";
import React, { useEffect, useRef, useState } from "react";
import { Alert, Animated, Easing, Text, View } from "react-native";
import { StatusBar } from "expo-status-bar";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { responsiveFontSize } from "@/lib/theme/colors";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  cardInformationSchema,
  CardInformationFormData,
} from "@/src/utils/validationSchemas";
import { FormField } from "@/src/components/forms/FormField";
import { FormPicker } from "@/src/components/forms/FormPicker";

// Use the type from validation schemas
type FormData = CardInformationFormData;

export default function CardInformationScreen() {
  const theme = useTheme();
  const params = useLocalSearchParams<{
    businessName?: string;
    email?: string;
    redirect?: string;
    message?: string;
  }>();

  const [initialFormData, setInitialFormData] = useState<FormData>({
    memberName: "",
    title: "",
    phone: "",
    businessCategory: "",
    businessSlug: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [showErrorRecovery, setShowErrorRecovery] = useState(false);
  const [isFormReady, setIsFormReady] = useState(false);

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    formState: { isValid },
    reset: resetForm,
    watch,
    setValue,
  } = useForm<FormData>({
    resolver: yupResolver(cardInformationSchema),
    defaultValues: initialFormData,
    mode: "onChange",
  });

  // Enhanced error handling
  const errorHandler = useAuthErrorHandler({
    maxRetries: 3,
    showToastOnError: true,
    showAlertOnError: false,
    context: "CardInformationScreen",
  });

  // Refs for bottom sheet pickers
  const categoryPickerRef = useRef<CategoryBottomSheetPickerRef>(null);

  // Animation for spinner
  const spinValue = useRef(new Animated.Value(0)).current;

  // Use slug validation hook
  const {
    isChecking: isCheckingSlug,
    isAvailable: slugAvailable,
    error: slugError,
    validateSlug,
    reset,
  } = useSlugValidation();

  // Start/stop spinner animation based on isCheckingSlug
  useEffect(() => {
    if (isCheckingSlug) {
      // Start spinning animation
      const spinAnimation = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      );
      spinAnimation.start();
      return () => spinAnimation.stop();
    } else {
      // Reset animation
      spinValue.setValue(0);
    }
  }, [isCheckingSlug, spinValue]);

  // Load existing onboarding data on component mount
  useEffect(() => {
    const loadExistingData = async () => {
      try {
        const existingData = await getOnboardingData();
        if (existingData) {
          setInitialFormData((prev) => ({
            ...prev,
            memberName: existingData.memberName || prev.memberName,
            title: existingData.title || prev.title,
            phone: existingData.phone || prev.phone,
            businessCategory:
              existingData.businessCategory || prev.businessCategory,
            businessSlug: existingData.businessSlug || prev.businessSlug,
          }));

          // Pre-filled slug from existing data
        }
      } catch (error) {
        console.error("Error loading existing onboarding data:", error);
      } finally {
        setIsFormReady(true);
      }
    };

    loadExistingData();
  }, []);

  // Reset form when initial data changes
  useEffect(() => {
    resetForm(initialFormData);
  }, [initialFormData, resetForm]);

  // Remove auto slug generation - let user manually enter their business slug

  // Watch for slug changes and validate
  const currentSlug = watch("businessSlug");
  useEffect(() => {
    if (currentSlug && currentSlug.length >= 3) {
      validateSlug(currentSlug);
    } else if (
      currentSlug &&
      currentSlug.length > 0 &&
      currentSlug.length < 3
    ) {
      // Reset validation state for slugs that are too short
      reset();
    }
  }, [currentSlug, validateSlug, reset]);

  const handleFormSubmit = async (values: FormData) => {
    // Manual checks for slug availability and validation status
    if (isCheckingSlug) {
      Alert.alert("Please wait", "Checking URL availability...");
      return;
    }

    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsLoading(true);

        // Save current form data to AsyncStorage
        await saveOnboardingData(values);

        // Navigate to next step with all form data
        let nextUrl = "/(onboarding)/address-information";
        const searchParams = new URLSearchParams();

        // Pass previous step data
        if (params.businessName)
          searchParams.append("businessName", params.businessName);
        if (params.email) searchParams.append("email", params.email);

        // Pass current form data
        Object.entries(values).forEach(([key, value]) => {
          if (value) {
            searchParams.append(key, value);
          }
        });

        // Pass redirect parameters if available
        if (params.redirect) {
          searchParams.append("redirect", params.redirect);
        }

        if (params.message) {
          searchParams.append("message", params.message);
        }

        if (searchParams.toString()) {
          nextUrl += `?${searchParams.toString()}`;
        }

        router.push(nextUrl);
        return { success: true };
      },
      onSuccess: () => {
        setIsLoading(false);
      },
      onError: () => {
        setIsLoading(false);
        setShowErrorRecovery(true);
      },
      context: "SaveCardInformation",
    });
  };

  const handleBack = () => {
    router.back();
  };

  // Handle category selection
  const handleCategorySelect = (category: string | null) => {
    if (category) {
      setValue("businessCategory", category);
    }
  };

  if (!isFormReady) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: theme.colors.background,
        }}
      >
        <Text style={{ color: theme.colors.textPrimary }}>Loading...</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar
        style={theme.isDark ? "light" : "dark"}
        backgroundColor={theme.colors.background}
      />



      {/* Error Recovery Modal */}
      {showErrorRecovery && errorHandler.hasError && (
        <ErrorRecovery
          error={errorHandler.error!}
          onRetry={() => {
            setShowErrorRecovery(false);
            errorHandler.clearError();
            // Re-trigger form submission if needed, or let user retry via UI
          }}
          onDismiss={() => {
            setShowErrorRecovery(false);
            errorHandler.clearError();
          }}
          isRetrying={errorHandler.isLoading}
          retryCount={errorHandler.retryCount}
          maxRetries={3}
          style={{
            position: "absolute",
            top: 100,
            left: 0,
            right: 0,
            zIndex: 1000,
          }}
        />
      )}

      <OnboardingContainer
        currentStep={2}
        totalSteps={4}
        onBack={handleBack}
        onNext={handleSubmit(handleFormSubmit)}
        nextButtonText={
          isLoading || errorHandler.isLoading ? "Loading..." : "Continue"
        }
        isNextDisabled={
          isLoading ||
          errorHandler.isLoading ||
          !errorHandler.isOnline ||
          !isValid ||
          isCheckingSlug ||
          slugAvailable !== true
        }
        showProgress={false}
      >
        <View
          style={{ gap: theme.spacing.md, paddingHorizontal: theme.spacing.xs }}
        >
          <View
            style={{ alignItems: "center", marginBottom: theme.spacing.lg }}
          >
            <Text
              style={{
                fontSize: theme.typography.fontSize.xxl,
                fontWeight: "700",
                color: theme.colors.textPrimary,
                textAlign: "center",
                marginBottom: theme.spacing.xs,
              }}
            >
              Card Information
            </Text>
            <Text
              style={{
                fontSize: theme.typography.fontSize.base,
                color: theme.colors.textSecondary,
                textAlign: "center",
                lineHeight:
                  theme.typography.lineHeight.normal *
                  theme.typography.fontSize.base,
              }}
            >
              Let&apos;s create your unique digital business card
            </Text>
          </View>

          {/* Member Name Field */}
          <FormField
            control={control}
            name="memberName"
            label="Your Name"
            placeholder="e.g., John Doe"
            leftIcon={<User size={20} color="#D4AF37" />}
            keyboardType="default"
            autoCapitalize="words"
            autoComplete="name"
          />

          {/* Title Field */}
          <FormField
            control={control}
            name="title"
            label="Your Title/Designation"
            placeholder="e.g., Founder, Manager"
            leftIcon={<Briefcase size={20} color="#D4AF37" />}
            keyboardType="default"
            autoCapitalize="words"
            autoComplete="off"
          />

          {/* Phone Field */}
          <FormField
            control={control}
            name="phone"
            label="Primary Phone Number"
            placeholder="9876543210"
            leftIcon={<Phone size={20} color="#D4AF37" />}
            keyboardType="phone-pad"
            autoCapitalize="none"
            autoComplete="tel"
            maxLength={10}
          />

          {/* Business Category Picker */}
          <FormPicker
            control={control}
            name="businessCategory"
            label="Business Category"
            placeholder="Select your business category"
            options={BUSINESS_CATEGORIES.map((cat) => ({
              label: cat.name,
              value: cat.name,
            }))}
            onPress={() => categoryPickerRef.current?.present()}
            leftIcon={<Briefcase size={20} color="#D4AF37" />}
          />

          {/* Business Slug Field */}
          <FormField
            control={control}
            name="businessSlug"
            label="Choose Your Card URL"
            placeholder="e.g., my-awesome-business"
            leftIcon={<Link size={20} color={theme.colors.primary} />}
            keyboardType="default"
            autoCapitalize="none"
            autoComplete="off"
            maxLength={30}
          />

          {/* Slug status indicator */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginTop: -theme.spacing.lg /* Further reduced spacing */,
              paddingHorizontal: theme.spacing.xs,
            }}
          >
            {isCheckingSlug && (
              <Animated.View
                style={{
                  transform: [
                    {
                      rotate: spinValue.interpolate({
                        inputRange: [0, 1],
                        outputRange: ["0deg", "360deg"],
                      }),
                    },
                  ],
                }}
              >
                <Loader2 size={16} color={theme.colors.primary} />
              </Animated.View>
            )}
            {!isCheckingSlug && slugAvailable === true && (
              <Ionicons
                name="checkmark-circle"
                size={16}
                color={theme.colors.success}
              />
            )}
            {!isCheckingSlug && slugAvailable === false && (
              <Ionicons
                name="close-circle"
                size={16}
                color={theme.colors.error}
              />
            )}

            {/* Status text */}
            {isCheckingSlug && (
              <Text
                style={{
                  color: theme.colors.textSecondary,
                  fontSize: 12,
                  marginLeft: theme.spacing.xs,
                }}
              >
                Checking availability...
              </Text>
            )}
            {!isCheckingSlug && slugAvailable === true && (
              <Text
                style={{
                  color: theme.colors.success,
                  fontSize: 12,
                  marginLeft: theme.spacing.xs,
                }}
              >
                ✓ URL is available!
              </Text>
            )}
            {!isCheckingSlug &&
              slugAvailable === false &&
              watch("businessSlug") && (
                <Text
                  style={{
                    color: theme.colors.error,
                    fontSize: 12,
                    fontWeight: "500",
                    marginLeft: theme.spacing.xs,
                  }}
                >
                  {slugError || "This URL is already taken."}
                </Text>
              )}
          </View>

          {/* URL Preview */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginTop: theme.spacing.sm /* Adjusted spacing */,
              paddingHorizontal: theme.spacing.sm,
              paddingVertical: theme.spacing.xs,
              backgroundColor: theme.colors.muted,
              borderRadius: theme.borderRadius.md,
            }}
          >
            <Ionicons
              name="link"
              size={responsiveFontSize(16)}
              color={
                theme.isDark ? theme.brandColors.gold : theme.colors.primary
              }
              style={{ marginRight: theme.spacing.xs }}
            />
            <Text
              style={{
                color: theme.colors.textSecondary,
                fontSize: theme.typography.fontSize.sm,
              }}
            >
              dukancard.in/
            </Text>
            <Text
              style={{
                color: watch("businessSlug")
                  ? theme.colors.textPrimary
                  : theme.colors.textSecondary,
                fontSize: theme.typography.fontSize.sm,
                fontWeight: watch("businessSlug") ? "600" : "400",
              }}
            >
              {watch("businessSlug") || "your-business-name"}
            </Text>
          </View>

          {/* Inline Error Handler */}
          {errorHandler.hasError && !showErrorRecovery && (
            <InlineErrorHandler
              error={errorHandler.error}
              onRetry={() => {
                errorHandler.clearError();
                handleSubmit(handleFormSubmit)();
              }}
              onDismiss={() => errorHandler.clearError()}
              isRetrying={errorHandler.isLoading}
              showRetry={errorHandler.canRetryOperation}
            />
          )}
        </View>
      </OnboardingContainer>

      {/* Category Bottom Sheet Picker */}
      <CategoryBottomSheetPicker
        ref={categoryPickerRef}
        selectedCategory={watch("businessCategory")}
        onCategorySelect={handleCategorySelect}
      />
    </GestureHandlerRootView>
  );
}
