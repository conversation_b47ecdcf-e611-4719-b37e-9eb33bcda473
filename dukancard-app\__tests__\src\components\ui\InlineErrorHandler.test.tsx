import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { InlineErrorHandler } from '@/src/components/ui/InlineErrorHandler';
import { AppError, createAppError } from '@/src/utils/errorHandling';
import { useTheme } from '@/src/hooks/useTheme';

// Mock hooks
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      warning: '#F59E0B',
      error: '#EF4444',
      textSecondary: '#666666',
    },
    borderRadius: { sm: 6 },
    spacing: { sm: 8, xs: 4 },
    typography: {
      fontSize: { sm: 14, xs: 12 },
      fontWeight: { semibold: '600', medium: '500' },
      lineHeight: { normal: 1.5 },
    },
  }),
}));

const mockOnRetry = jest.fn();
const mockOnDismiss = jest.fn();

const networkError = createAppError(
  'network',
  'Connection Issue',
  'Please check your internet connection.'
);
const serverError = createAppError(
  'server',
  'Server Error',
  'Something went wrong on our end.'
);

const renderComponent = (props: Partial<React.ComponentProps<typeof InlineErrorHandler>> & { error: AppError | null }) => {
  return render(
    <InlineErrorHandler
      onRetry={mockOnRetry}
      onDismiss={mockOnDismiss}
      {...props}
    />
  );
};

describe('<InlineErrorHandler />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('does not render if error is null', () => {
    const { toJSON } = renderComponent({ error: null });
    expect(toJSON()).toBeNull();
  });

  it('renders correctly for a network error', () => {
    const { getByText, toJSON } = renderComponent({ error: networkError });
    expect(getByText('Connection Issue')).toBeTruthy();
    expect(getByText('Please check your internet connection.')).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders correctly for a server error', () => {
    const { getByText, toJSON } = renderComponent({ error: serverError });
    expect(getByText('Server Error')).toBeTruthy();
    expect(getByText('Something went wrong on our end.')).toBeTruthy();
    expect(toJSON()).toMatchSnapshot();
  });

  it('calls onRetry when the retry button is pressed', () => {
    const { getByText } = renderComponent({ error: networkError });
    fireEvent.press(getByText('Retry'));
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('calls onDismiss when the dismiss button is pressed', () => {
    const { getByLabelText } = renderComponent({ error: networkError });
    fireEvent.press(getByLabelText('Dismiss'));
    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });

  it('shows a loading indicator when retrying', () => {
    const { getByText, queryByText } = renderComponent({
      error: networkError,
      isRetrying: true,
    });
    expect(queryByText('Retry')).toBeNull();
    expect(getByText('Retrying...')).toBeTruthy();
  });

  it('hides the retry button when showRetry is false', () => {
    const { queryByText } = renderComponent({
      error: networkError,
      showRetry: false,
    });
    expect(queryByText('Retry')).toBeNull();
  });

  it('hides the dismiss button when showDismiss is false', () => {
    const { queryByLabelText } = renderComponent({
      error: networkError,
      showDismiss: false,
    });
    expect(queryByLabelText('Dismiss')).toBeNull();
  });
});